'use client'

import { useEffect, useRef, useState } from 'react'
import * as echarts from 'echarts'
import { Package, AlertTriangle, CheckCircle, Clock } from 'lucide-react'

interface InventoryData {
  category: string
  value: number
  status: 'in-stock' | 'low-stock' | 'out-of-stock'
  color: string
}

interface InventoryChartProps {
  data: InventoryData[]
  title?: string
  height?: number
  showLegend?: boolean
}

export default function InventoryChart({
  data,
  title = "Inventory Distribution",
  height = 400,
  showLegend = true
}: InventoryChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  // Calculate metrics
  const totalItems = data.reduce((sum, item) => sum + item.value, 0)
  const inStockItems = data.filter(item => item.status === 'in-stock').reduce((sum, item) => sum + item.value, 0)
  const lowStockItems = data.filter(item => item.status === 'low-stock').reduce((sum, item) => sum + item.value, 0)
  const outOfStockItems = data.filter(item => item.status === 'out-of-stock').reduce((sum, item) => sum + item.value, 0)

  const healthScore = ((inStockItems / totalItems) * 100).toFixed(1)

  useEffect(() => {
    if (!chartRef.current || !data.length) return

    chartInstance.current = echarts.init(chartRef.current, 'light')

    const option: echarts.EChartsOption = {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#374151'
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: {
          color: '#374151'
        },
        formatter: function (params: any) {
          const percentage = params.percent
          const value = params.value
          const name = params.name
          
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 8px;">${name}</div>
              <div style="margin: 4px 0;">Items: ${value.toLocaleString()}</div>
              <div style="margin: 4px 0;">Percentage: ${percentage}%</div>
            </div>
          `
        }
      },
      legend: showLegend ? {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
        textStyle: {
          color: '#6b7280'
        },
        formatter: function (name: string) {
          const item = data.find(d => d.category === name)
          return `${name} (${item?.value || 0})`
        }
      } : undefined,
      series: [
        {
          name: 'Inventory',
          type: 'pie',
          radius: ['40%', '70%'],
          center: showLegend ? ['60%', '50%'] : ['50%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
              color: '#374151',
              formatter: function (params: any) {
                return `${params.name}\n${params.value} items\n${params.percent}%`
              }
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          data: data.map(item => ({
            value: item.value,
            name: item.category,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                { offset: 0, color: item.color },
                { offset: 1, color: item.color + '80' }
              ])
            }
          })),
          animationType: 'scale',
          animationEasing: 'elasticOut',
          animationDelay: function (idx: number) {
            return Math.random() * 200
          }
        }
      ],
      animation: true,
      animationDuration: 1000
    }

    chartInstance.current.setOption(option)

    // Handle click events
    chartInstance.current.on('click', function (params: any) {
      setSelectedCategory(params.name === selectedCategory ? null : params.name)
    })

    setTimeout(() => setIsLoading(false), 500)

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, title, height, showLegend, selectedCategory])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in-stock':
        return <CheckCircle className="h-4 w-4 text-success" />
      case 'low-stock':
        return <AlertTriangle className="h-4 w-4 text-warning" />
      case 'out-of-stock':
        return <Clock className="h-4 w-4 text-error" />
      default:
        return <Package className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in-stock':
        return 'text-success'
      case 'low-stock':
        return 'text-warning'
      case 'out-of-stock':
        return 'text-error'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className="card-elevated p-6 hover-lift animate-fade-in stagger-3">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-orange-500 to-red-600 p-2 rounded-lg">
            <Package className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-heading-medium text-gray-900">{title}</h3>
            <p className="text-body-small text-muted">Stock levels by category</p>
          </div>
        </div>

        {/* Health Score */}
        <div className="text-right">
          <div className="flex items-center space-x-1">
            <CheckCircle className="h-4 w-4 text-success" />
            <span className="text-sm font-semibold text-success">{healthScore}%</span>
          </div>
          <p className="text-body-small text-muted">Health Score</p>
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-orange-600 border-t-transparent rounded-full animate-spin" />
              <span className="text-body-medium text-muted">Loading inventory data...</span>
            </div>
          </div>
        )}
        <div
          ref={chartRef}
          style={{ height: `${height}px`, width: '100%' }}
          className="transition-opacity duration-300"
        />
      </div>

      {/* Category Details */}
      <div className="mt-6 space-y-3">
        <h4 className="text-label-large text-gray-900 mb-3">Category Breakdown</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {data.map((item, index) => (
            <div
              key={item.category}
              className={`
                p-3 rounded-lg border transition-all duration-200 cursor-pointer
                ${selectedCategory === item.category
                  ? 'border-primary bg-primary-light'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }
              `}
              onClick={() => setSelectedCategory(selectedCategory === item.category ? null : item.category)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(item.status)}
                  <span className="text-body-medium font-medium text-gray-900">
                    {item.category}
                  </span>
                </div>
                <div className="text-right">
                  <p className="text-body-medium font-semibold text-gray-900">
                    {item.value.toLocaleString()}
                  </p>
                  <p className={`text-body-small ${getStatusColor(item.status)}`}>
                    {item.status.replace('-', ' ')}
                  </p>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="mt-2">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-500"
                    style={{
                      width: `${(item.value / totalItems) * 100}%`,
                      backgroundColor: item.color
                    }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <CheckCircle className="h-4 w-4 text-success" />
            <p className="text-body-small text-muted">In Stock</p>
          </div>
          <p className="text-heading-small text-success">{inStockItems.toLocaleString()}</p>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <AlertTriangle className="h-4 w-4 text-warning" />
            <p className="text-body-small text-muted">Low Stock</p>
          </div>
          <p className="text-heading-small text-warning">{lowStockItems.toLocaleString()}</p>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <Clock className="h-4 w-4 text-error" />
            <p className="text-body-small text-muted">Out of Stock</p>
          </div>
          <p className="text-heading-small text-error">{outOfStockItems.toLocaleString()}</p>
        </div>
      </div>
    </div>
  )
}
