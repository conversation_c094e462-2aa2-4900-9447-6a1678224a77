'use client'

import { useState, useRef, useEffect } from 'react'
import Image from 'next/image'
import { ImageIcon, AlertCircle } from 'lucide-react'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  loading?: 'lazy' | 'eager'
  onLoad?: () => void
  onError?: () => void
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = "",
  priority = false,
  quality = 75,
  placeholder = 'blur',
  blurDataURL,
  sizes,
  fill = false,
  objectFit = 'cover',
  loading = 'lazy',
  onLoad,
  onError
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [loadTime, setLoadTime] = useState<number>(0)
  const startTime = useRef<number>()

  useEffect(() => {
    startTime.current = performance.now()
  }, [src])

  const handleLoad = () => {
    setIsLoading(false)
    if (startTime.current) {
      const time = performance.now() - startTime.current
      setLoadTime(time)
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🖼️ Image loaded: ${src} (${time.toFixed(2)}ms)`)
      }
    }
    onLoad?.()
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
    onError?.()
    
    if (process.env.NODE_ENV === 'development') {
      console.error(`🖼️ Image failed to load: ${src}`)
    }
  }

  // Generate blur placeholder if not provided
  const generateBlurDataURL = (w: number = 10, h: number = 10) => {
    const canvas = document.createElement('canvas')
    canvas.width = w
    canvas.height = h
    const ctx = canvas.getContext('2d')
    
    if (ctx) {
      // Create a simple gradient blur effect
      const gradient = ctx.createLinearGradient(0, 0, w, h)
      gradient.addColorStop(0, '#f3f4f6')
      gradient.addColorStop(1, '#e5e7eb')
      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, w, h)
    }
    
    return canvas.toDataURL()
  }

  const defaultBlurDataURL = blurDataURL || generateBlurDataURL(width, height)

  // Error state
  if (hasError) {
    return (
      <div 
        className={`
          flex items-center justify-center bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg
          ${className}
        `}
        style={{ width, height }}
      >
        <div className="text-center p-4">
          <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">Failed to load image</p>
          <p className="text-xs text-gray-400 mt-1 truncate max-w-[200px]">{alt}</p>
        </div>
      </div>
    )
  }

  // Loading state overlay
  const LoadingOverlay = () => (
    <div className={`
      absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75 transition-opacity duration-300
      ${isLoading ? 'opacity-100' : 'opacity-0 pointer-events-none'}
    `}>
      <div className="text-center">
        <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-2"></div>
        <p className="text-xs text-gray-500">Loading...</p>
      </div>
    </div>
  )

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <Image
        src={src}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        priority={priority}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={placeholder === 'blur' ? defaultBlurDataURL : undefined}
        sizes={sizes}
        className={`
          transition-opacity duration-300
          ${isLoading ? 'opacity-0' : 'opacity-100'}
          ${fill ? `object-${objectFit}` : ''}
        `}
        style={!fill ? { objectFit } : undefined}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
      />
      
      <LoadingOverlay />
      
      {/* Development info */}
      {process.env.NODE_ENV === 'development' && !isLoading && !hasError && (
        <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          {loadTime.toFixed(0)}ms
        </div>
      )}
    </div>
  )
}

// Gallery component with lazy loading
interface ImageGalleryProps {
  images: Array<{
    src: string
    alt: string
    width?: number
    height?: number
  }>
  columns?: number
  gap?: number
  className?: string
}

export function ImageGallery({
  images,
  columns = 3,
  gap = 4,
  className = ""
}: ImageGalleryProps) {
  const [visibleImages, setVisibleImages] = useState<Set<number>>(new Set())
  const observerRef = useRef<IntersectionObserver>()

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0')
            setVisibleImages(prev => new Set([...prev, index]))
          }
        })
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    )

    return () => {
      observerRef.current?.disconnect()
    }
  }, [])

  const handleImageRef = (element: HTMLDivElement | null, index: number) => {
    if (element && observerRef.current) {
      element.setAttribute('data-index', index.toString())
      observerRef.current.observe(element)
    }
  }

  return (
    <div 
      className={`grid gap-${gap} ${className}`}
      style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
    >
      {images.map((image, index) => (
        <div
          key={index}
          ref={(el) => handleImageRef(el, index)}
          className="aspect-square bg-gray-100 rounded-lg overflow-hidden"
        >
          {visibleImages.has(index) ? (
            <OptimizedImage
              src={image.src}
              alt={image.alt}
              fill
              objectFit="cover"
              className="w-full h-full"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <ImageIcon className="h-8 w-8 text-gray-400" />
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

// Avatar component with optimized loading
interface AvatarProps {
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fallback?: string
  className?: string
}

export function Avatar({
  src,
  alt,
  size = 'md',
  fallback,
  className = ""
}: AvatarProps) {
  const [hasError, setHasError] = useState(false)

  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base',
    xl: 'w-16 h-16 text-lg'
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (!src || hasError) {
    return (
      <div className={`
        ${sizeClasses[size]} 
        bg-gradient-to-br from-blue-500 to-purple-600 
        rounded-full flex items-center justify-center text-white font-medium
        ${className}
      `}>
        {fallback || getInitials(alt)}
      </div>
    )
  }

  return (
    <div className={`${sizeClasses[size]} rounded-full overflow-hidden ${className}`}>
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        objectFit="cover"
        quality={90}
        onError={() => setHasError(true)}
      />
    </div>
  )
}

// Product image component with multiple sizes
interface ProductImageProps {
  src: string
  alt: string
  variant?: 'thumbnail' | 'card' | 'hero'
  className?: string
}

export function ProductImage({
  src,
  alt,
  variant = 'card',
  className = ""
}: ProductImageProps) {
  const variants = {
    thumbnail: {
      width: 80,
      height: 80,
      quality: 60,
      sizes: '80px'
    },
    card: {
      width: 300,
      height: 200,
      quality: 75,
      sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
    },
    hero: {
      width: 800,
      height: 600,
      quality: 90,
      sizes: '(max-width: 768px) 100vw, 80vw',
      priority: true
    }
  }

  const config = variants[variant]

  return (
    <div className={`relative bg-gray-100 rounded-lg overflow-hidden ${className}`}>
      <OptimizedImage
        src={src}
        alt={alt}
        width={config.width}
        height={config.height}
        quality={config.quality}
        sizes={config.sizes}
        priority={config.priority}
        className="w-full h-full"
      />
    </div>
  )
}
