'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import SalesChart from '@/components/charts/SalesChart'
import RevenueChart from '@/components/charts/RevenueChart'
import InventoryChart from '@/components/charts/InventoryChart'
import CustomerChart from '@/components/charts/CustomerChart'
import { BarChart3, TrendingUp, Package, Users, Download, RefreshCw } from 'lucide-react'

export default function AnalyticsPage() {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  // Enhanced sample data with more realistic business metrics
  const salesData = [
    { date: 'Mon', sales: 85, target: 100, revenue: 2100 },
    { date: 'Tue', sales: 92, target: 100, revenue: 2300 },
    { date: 'Wed', sales: 78, target: 100, revenue: 1950 },
    { date: 'Thu', sales: 95, target: 100, revenue: 2375 },
    { date: 'Fri', sales: 88, target: 100, revenue: 2200 },
    { date: 'Sat', sales: 110, target: 100, revenue: 2750 },
    { date: 'Sun', sales: 75, target: 100, revenue: 1875 },
  ]

  const revenueData = [
    { month: 'Jan', revenue: 45000, profit: 12000, expenses: 33000 },
    { month: 'Feb', revenue: 52000, profit: 15000, expenses: 37000 },
    { month: 'Mar', revenue: 48000, profit: 13500, expenses: 34500 },
    { month: 'Apr', revenue: 61000, profit: 18000, expenses: 43000 },
    { month: 'May', revenue: 55000, profit: 16500, expenses: 38500 },
    { month: 'Jun', revenue: 67000, profit: 20000, expenses: 47000 },
  ]

  const inventoryData = [
    { category: 'Beverages', value: 150, status: 'in-stock' as const, color: '#3b82f6' },
    { category: 'Snacks', value: 89, status: 'low-stock' as const, color: '#f59e0b' },
    { category: 'Household', value: 45, status: 'in-stock' as const, color: '#10b981' },
    { category: 'Personal Care', value: 23, status: 'out-of-stock' as const, color: '#ef4444' },
    { category: 'Condiments', value: 67, status: 'in-stock' as const, color: '#8b5cf6' },
    { category: 'Frozen Foods', value: 34, status: 'low-stock' as const, color: '#06b6d4' },
  ]

  const customerData = [
    { month: 'Jan', newCustomers: 12, activeCustomers: 45, churnedCustomers: 2, satisfaction: 4.2 },
    { month: 'Feb', newCustomers: 18, activeCustomers: 58, churnedCustomers: 3, satisfaction: 4.3 },
    { month: 'Mar', newCustomers: 15, activeCustomers: 67, churnedCustomers: 1, satisfaction: 4.5 },
    { month: 'Apr', newCustomers: 22, activeCustomers: 78, churnedCustomers: 4, satisfaction: 4.4 },
    { month: 'May', newCustomers: 19, activeCustomers: 85, churnedCustomers: 2, satisfaction: 4.6 },
    { month: 'Jun', newCustomers: 25, activeCustomers: 95, churnedCustomers: 3, satisfaction: 4.7 },
  ]

  const handleRefresh = async () => {
    setIsRefreshing(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsRefreshing(false)
  }

  const handleExport = () => {
    // In a real app, this would generate and download a report
    console.log('Exporting analytics report...')
  }

  return (
    <DashboardLayout
      pageTitle="Analytics Dashboard"
      pageSubtitle="Comprehensive business insights and performance metrics"
    >
      <div className="space-y-8">
        {/* Analytics Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-xl">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-display-medium text-gray-900">Business Analytics</h1>
              <p className="text-body-medium text-muted">Real-time insights and performance tracking</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Date Range Selector */}
            <div className="flex items-center space-x-1 bg-white border border-gray-200 rounded-lg p-1">
              {(['7d', '30d', '90d', '1y'] as const).map((range) => (
                <button
                  key={range}
                  onClick={() => setDateRange(range)}
                  className={`
                    px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200
                    ${dateRange === range
                      ? 'bg-primary text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }
                  `}
                >
                  {range === '7d' ? '7 Days' : range === '30d' ? '30 Days' : range === '90d' ? '90 Days' : '1 Year'}
                </button>
              ))}
            </div>

            {/* Action Buttons */}
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="btn-secondary"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline">Refresh</span>
            </button>

            <button
              onClick={handleExport}
              className="btn-primary"
            >
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">Export</span>
            </button>
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card-elevated p-6 text-center hover-lift animate-fade-in stagger-1">
            <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-4">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-heading-small text-gray-900 mb-1">Revenue Growth</h3>
            <p className="text-display-medium text-blue-600 font-bold mb-1">+23.5%</p>
            <p className="text-body-small text-muted">vs last month</p>
          </div>

          <div className="card-elevated p-6 text-center hover-lift animate-fade-in stagger-2">
            <div className="bg-green-100 p-3 rounded-full w-fit mx-auto mb-4">
              <BarChart3 className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-heading-small text-gray-900 mb-1">Sales Performance</h3>
            <p className="text-display-medium text-green-600 font-bold mb-1">89.2%</p>
            <p className="text-body-small text-muted">target achievement</p>
          </div>

          <div className="card-elevated p-6 text-center hover-lift animate-fade-in stagger-3">
            <div className="bg-purple-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-heading-small text-gray-900 mb-1">Customer Satisfaction</h3>
            <p className="text-display-medium text-purple-600 font-bold mb-1">4.6/5.0</p>
            <p className="text-body-small text-muted">average rating</p>
          </div>

          <div className="card-elevated p-6 text-center hover-lift animate-fade-in stagger-4">
            <div className="bg-orange-100 p-3 rounded-full w-fit mx-auto mb-4">
              <Package className="h-6 w-6 text-orange-600" />
            </div>
            <h3 className="text-heading-small text-gray-900 mb-1">Inventory Health</h3>
            <p className="text-display-medium text-orange-600 font-bold mb-1">92.3%</p>
            <p className="text-body-small text-muted">stock availability</p>
          </div>
        </div>

        {/* Main Analytics Charts */}
        <div className="space-y-8">
          {/* Sales Performance - Full Width */}
          <div className="animate-fade-in stagger-1">
            <SalesChart 
              data={salesData} 
              title="Weekly Sales Performance"
              height={450}
              animated={true}
            />
          </div>

          {/* Revenue and Inventory - Side by Side */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
            <div className="animate-fade-in stagger-2">
              <RevenueChart 
                data={revenueData}
                title="Revenue & Profit Analysis"
                height={400}
              />
            </div>
            <div className="animate-fade-in stagger-3">
              <InventoryChart 
                data={inventoryData}
                title="Inventory Status Overview"
                height={400}
              />
            </div>
          </div>

          {/* Customer Analytics - Full Width */}
          <div className="animate-fade-in stagger-4">
            <CustomerChart 
              data={customerData}
              title="Customer Growth & Satisfaction"
              height={400}
            />
          </div>
        </div>

        {/* Analytics Insights */}
        <div className="card-elevated p-8 animate-fade-in stagger-5">
          <h2 className="text-heading-large text-gray-900 mb-6">Key Insights</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <h3 className="text-heading-small text-gray-900">Revenue Trends</h3>
              </div>
              <p className="text-body-medium text-gray-700">
                Revenue has shown consistent growth over the past 6 months, with a 23.5% increase compared to the previous period.
              </p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <h3 className="text-heading-small text-gray-900">Customer Retention</h3>
              </div>
              <p className="text-body-medium text-gray-700">
                Customer satisfaction scores have improved to 4.6/5.0, indicating strong customer loyalty and service quality.
              </p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <h3 className="text-heading-small text-gray-900">Inventory Management</h3>
              </div>
              <p className="text-body-medium text-gray-700">
                Inventory health is at 92.3% with some categories requiring attention to prevent stockouts.
              </p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
